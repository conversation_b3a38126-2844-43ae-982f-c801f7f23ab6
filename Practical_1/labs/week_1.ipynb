# %matplotlib inline is a magic function for displaying the image in the notebook
%matplotlib inline
import numpy as np
import cv2
import matplotlib.image as mpimg
from matplotlib import pyplot as plt

# fill in missing part
img = np.empty((100,100,3)) # replace this code and load your image
img = cv2.imread('../images/apple1.jpg')
img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

# display the image (Hint: use plt.imshow)
plt.imshow(img)

print(img.shape)

# Play with the images here 
# access image channels (RGB)

img_channels_b = img.copy()
img_channels_b[:,:,0] = 0
img_channels_b[:,:,1] = 0
# plt.imshow(img_channels_b) # blue

img_channels_g = img.copy()
img_channels_g[:,:,0] = 0
img_channels_g[:,:,2] = 0
# plt.imshow(img_channels_g) # green

img_channels_r = img.copy()
img_channels_r[:,:,1] = 0
img_channels_r[:,:,2] = 0
# plt.imshow(img_channels_r) # red

plt.subplot(1,3,1)
plt.imshow(img_channels_b, cmap='gray')
plt.subplot(1,3,2)
plt.imshow(img_channels_g, cmap='gray')
plt.subplot(1,3,3)
plt.imshow(img_channels_r, cmap='gray')



# swap channels
img_swap = img.copy()
img_swap[:,:,0] = img[:,:,2]
img_swap[:,:,2] = img[:,:,0]
plt.imshow(img_swap)


gray_img = np.empty((100,100,3)) # replace this code and load your image
gray_img = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
# display the image (Hint: use plt.imshow)
plt.imshow(gray_img, cmap='gray')

def to_gray(img):
    '''
    Convert and RGB image into gray scale
    :param img: RBG image with size HxWx3
    :return: gray scaled image of size HxW
    '''
    assert len(img.shape) == 3, f'Wrong input image dimensions, we expected an input of size HxWxC instead we got {img.shape}'
    h,w,c = img.shape
    gray_img = img[:,:,0] * 0.299 + img[:,:,1] * 0.587 + img[:,:,2] * 0.114 # Modify this part to convert the image onto gray scale

    assert gray_img.shape == (h,w), 'Wrong spacial dimensions, your method should only change the channel dimension'
    return gray_img

img_to_gray = to_gray(img)
plt.imshow(img_to_gray, cmap='gray')

transposed_img = np.empty((100,100,3)) # replace this code and load your image
transposed_img = cv2.imread('../images/apple1.jpg')
transposed_img = cv2.cvtColor(transposed_img, cv2.COLOR_BGR2RGB)
transposed_img = np.transpose(transposed_img, (1,0,2))
# display the transposed_img (Hint: use plt.imshow)
plt.imshow(transposed_img)

first_part = img
second_part = img
third_part = img
fourth_part = img

# display all parts

scaled_img = img # replace this part

axes = plt.subplots(2,1)
# display original image

# display scaled down image

def average_scale_down(img):
    '''
    Scale down and RGB image
    :param img: RBG image with size HxWx3
    :return: scaled_img: RBG scaled down image with size (H/4)x(W/4)x3
    '''
    assert len(img.shape) == 3, f'Wrong input image dimensions, we expected an input of size HxWxC instead we got {img.shape}'
    h,w,c = img.shape
    scaled_img = img # Modify this part to convert the image onto gray scale

    assert scaled_img.shape == (h//2,w//2, 3), f'Wrong spacial dimensions, your method should reduce the spacial dimensions by a factor of four we expect {(h//2,w//2, 3)} instead we have {scaled_img.shape}'
    return scaled_img